# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.venv/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files
test_*.py
debug_*.py
*_test.py
*_debug.py

# Temporary files
*.tmp
*.temp
temp_*
tmp_*

# Generated images (users download these, no need to store in git)
generated_images/
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.webp
*.tiff
# Exception: Keep sample images for documentation
!docs/images/
!examples/images/
!README_images/

# Logs
*.log
logs/

# API Keys and Configuration (CRITICAL - Never commit these!)
# config.py
.env.local
.env.production
.env.development
secrets.py
api_keys.py
secrets.json
api_keys.txt
*.key
*.pem

# Model Cache and Downloads (Large files)
.cache/
models/
checkpoints/
*.bin
*.safetensors
*.ckpt
*.pt
*.pth

# Gradio Specific
gradio_cached_examples/
flagged/
gradio_queue.db

# Hugging Face Cache
.huggingface/
transformers_cache/

# Documentation drafts
*_draft.md
*_temp.md

# Large files that should use Git LFS
*.zip
*.tar.gz
*.rar
*.7z
*.dmg
*.iso

# Backup files
*.bak
*.backup
*.old
*~

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# pytest cache
.pytest_cache/
