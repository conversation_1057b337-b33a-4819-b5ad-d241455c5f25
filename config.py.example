import os
from dotenv import load_dotenv
load_dotenv()

# API Keys - Set these in your environment or .env file
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "your_gemini_api_key_here")
STABILITY_API_KEY = os.getenv("STABILITY_API_KEY", "your_stability_api_key_here") 
REPLICATE_API_TOKEN = os.getenv("REPLICATE_API_TOKEN", "your_replicate_token_here")

# Instructions:
# 1. Copy this file to config.py
# 2. Replace the placeholder values with your actual API keys
# 3. Or create a .env file with:
#    GEMINI_API_KEY=your_actual_key
#    REPLICATE_API_TOKEN=your_actual_token
