import json
import requests
from config import GEMINI_API_KEY
from styles.model import StyleList
from styles.predefined import get_predefined_styles_with_prompt

def create_style_gen_prompt(user_prompt: str, num_styles: int) -> str:
    """Tạo prompt động dựa trên số lượng styles yêu cầu"""

    if user_prompt and user_prompt.strip():
        # Có user prompt - kết hợp với styles
        return f"""
You are an AI image generation expert. Given this original prompt:
"{user_prompt}"

Generate exactly {num_styles} different and unique image styles. Each style should have:
- Style name (short, in English)
- Brief description of the style characteristics (max 10 words)
- Complete prompt that PRIORITIZES the original content but applies the style

IMPORTANT: The user's original prompt content is the MAIN SUBJECT. The style should only modify HOW it's rendered, not WHAT is rendered.

Format the style_prompt as: "{user_prompt} in [style description] style, highly detailed"

Make them diverse: realistic, artistic, fantasy, minimalist, vintage, cyberpunk, watercolor, oil painting, anime, sketch, pop art, steampunk, impressionist, gothic, pixel art, art nouveau, surreal, noir, cartoon, abstract, etc.

Return ONLY a valid JSON array with exactly {num_styles} objects and no additional text or markdown:
[
  {{"name": "Style Name 1", "description": "Description of style 1", "style_prompt": "{user_prompt} in [style 1] style, highly detailed"}},
  {{"name": "Style Name 2", "description": "Description of style 2", "style_prompt": "{user_prompt} in [style 2] style, highly detailed"}},
  ... (continue for {num_styles} total styles)
]
"""
    else:
        # Không có user prompt - tạo styles ngẫu nhiên với generic subjects
        return f"""
You are an AI image generation expert. Generate exactly {num_styles} different and unique image styles with random creative subjects.

Each style should have:
- Style name (short, in English)
- Brief description of the style characteristics
- Complete creative prompt with a random interesting subject in that style (in English, concise but detailed)

Make them diverse in both style and subject matter. Use various subjects like: landscapes, portraits, animals, fantasy creatures, sci-fi scenes, abstract concepts, still life, architecture, nature, etc.

Style examples: realistic, artistic, fantasy, minimalist, vintage, cyberpunk, watercolor, oil painting, anime, sketch, pop art, steampunk, impressionist, gothic, pixel art, art nouveau, surreal, noir, cartoon, abstract, etc.

Return ONLY a valid JSON array with exactly {num_styles} objects and no additional text or markdown:
[
  {{"name": "Style Name 1", "description": "Description of style 1", "style_prompt": "Creative prompt with random subject in style 1"}},
  {{"name": "Style Name 2", "description": "Description of style 2", "style_prompt": "Creative prompt with random subject in style 2"}},
  ... (continue for {num_styles} total styles)
]
"""

def get_style_list_from_prompt(user_prompt: str = "", num_styles: int = 5) -> StyleList:
    """
    Tạo styles sử dụng Gemini AI

    Args:
        user_prompt (str): Prompt gốc từ user (có thể để trống để tạo styles ngẫu nhiên)
        num_styles (int): Số lượng styles cần tạo (3, 5, 8, 10)

    Returns:
        StyleList: Danh sách styles được tạo bởi AI
    """
    if num_styles not in [3, 5, 8, 10]:
        raise ValueError("Số lượng styles phải là 3, 5, 8 hoặc 10")

    # Xử lý user prompt
    user_prompt = user_prompt.strip() if user_prompt else ""
    prompt = create_style_gen_prompt(user_prompt, num_styles)

    # Sử dụng endpoint mới nhất cho Gemini 1.5 Flash
    endpoint = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key={GEMINI_API_KEY}"
    headers = {"Content-Type": "application/json"}

    body = {
        "contents": [{"parts": [{"text": prompt}]}],
        "generationConfig": {
            "temperature": 0.7,
            "topK": 1,
            "topP": 1,
            "maxOutputTokens": 4096,
        }
    }

    response = requests.post(endpoint, headers=headers, json=body)
    print(f"Status Code: {response.status_code}")
    print("========= RESPONSE RAW FROM GEMINI =========")
    print(f"Response: {response.text}")
    print("============================================")
    if response.status_code != 200:
        raise ValueError(f"Gemini API error: {response.status_code} - {response.text}")

    data = response.json()

    try:
        text = data['candidates'][0]['content']['parts'][0]['text']
        print("==== RESPONSE RAW TEXT FROM GEMINI ====")
        print(text)  # 👈 in ra để xem Gemini trả về gì

        # Làm sạch text response (loại bỏ markdown nếu có)
        clean_text = text.strip()

        # Loại bỏ các markdown wrapper
        if clean_text.startswith('```json'):
            clean_text = clean_text[7:]
        elif clean_text.startswith('```'):
            clean_text = clean_text[3:]

        if clean_text.endswith('```'):
            clean_text = clean_text[:-3]

        clean_text = clean_text.strip()

        start_idx = clean_text.find('[')
        end_idx = clean_text.rfind(']')

        if start_idx == -1 or end_idx == -1:
            print(f"DEBUG: Could not find JSON array in text: {clean_text[:200]}...")
            raise ValueError("Không tìm thấy JSON array trong response")

        json_text = clean_text[start_idx:end_idx+1]
        print(f"DEBUG: Extracted JSON: {json_text[:200]}...")

        # Thử parse JSON
        try:
            styles_data = json.loads(json_text)
        except json.JSONDecodeError as e:
            # Nếu JSON bị cắt, thử fix bằng cách thêm closing brackets
            print(f"DEBUG: JSON parse failed, trying to fix: {str(e)}")

            # Đếm số objects đã hoàn thành
            completed_objects = json_text.count('"}')
            if completed_objects > 0:
                # Cắt tại object cuối cùng hoàn chỉnh
                last_complete = json_text.rfind('"}')
                if last_complete != -1:
                    fixed_json = json_text[:last_complete+2] + ']'
                    print(f"DEBUG: Trying fixed JSON: {fixed_json[:200]}...")
                    styles_data = json.loads(fixed_json)
                else:
                    raise e
            else:
                raise e

        # Validate structure
        if not isinstance(styles_data, list):
            raise ValueError("Response không phải là array")

        for style in styles_data:
            if not all(key in style for key in ['name', 'description', 'style_prompt']):
                raise ValueError("Style thiếu các field bắt buộc")

        return styles_data

    except json.JSONDecodeError as e:
        raise ValueError(f"Lỗi JSON từ Gemini: {str(e)}\nText: {text}")
    except KeyError as e:
        raise ValueError(f"Lỗi cấu trúc response từ Gemini: {str(e)}\nRaw: {data}")
    except Exception as e:
        raise ValueError(f"Lỗi không xác định: {str(e)}\nRaw: {data}")

def get_predefined_styles_list(user_prompt: str, num_styles: int = 5) -> StyleList:
    """
    Lấy danh sách style có sẵn kết hợp với user prompt

    Args:
        user_prompt (str): Prompt gốc từ user
        num_styles (int): Số lượng styles (ignored - always returns 4 predefined styles)

    Returns:
        StyleList: Danh sách 4 styles từ predefined list
    """
    try:
        # Predefined mode luôn trả về tất cả 4 styles có sẵn
        all_styles = get_predefined_styles_with_prompt(user_prompt)
        return all_styles  # Trả về tất cả 4 styles
    except Exception as e:
        raise ValueError(f"Lỗi khi lấy predefined styles: {str(e)}")
