name: Deploy to Hugging Face Space

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v3
        with:
          lfs: true

      - name: Set up Git
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "ductranvnggames"

      - name: Push to Hugging Face Space
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
          HF_USERNAME: ${{ secrets.HF_USERNAME }}
          SPACE_NAME: ${{ secrets.SPACE_NAME }}
        run: |
          git clone https://$HF_USERNAME:${HF_TOKEN}@huggingface.co/spaces/$HF_USERNAME/$SPACE_NAME hf-space
          rsync -av --exclude='.git' ./ hf-space/
          cd hf-space
          git add .
          git commit -m "Auto update from GitHub" || echo "No changes to commit"
          git push origin main