from .model import StyleList

# 6 core predefined styles cho image generation
PREDEFINED_STYLES: StyleList = [
    {
        "name": "Comic",
        "description": "A dynamic comic-style artwork with bold lines and vivid colors.",
        "style_prompt": "highly detailed comic-style illustration, featuring bold black ink outlines, vivid and saturated colors, dramatic and directional lighting, dynamic poses, expressive facial features, halftone shading patterns, exaggerated motion lines, and high-contrast composition inspired by classic American graphic novels and modern superhero comics"
    },
    {
        "name": "Anime",
        "description": "Cinematic anime style with expressive features and vivid detail.",
        "style_prompt": "highly detailed anime-style illustration, featuring clean and sharp linework, vivid and saturated colors, large expressive eyes with glossy highlights, subtle blush and facial nuances, smooth gradient shading, soft ambient glow, dramatic camera angles, and polished cinematic lighting inspired by high-quality Japanese animation and modern anime films"
    },
    {
        "name": "CasualGame",
        "description": "Playful, colorful style perfect for mobile games.",
        "style_prompt": "adorable and colorful illustration in a casual game art style, featuring soft rounded shapes, smooth and gentle shading, warm and inviting color palettes, simplified forms with expressive faces, minimal detail for clarity at small sizes, and a friendly, non-intimidating design ideal for mobile game characters, environments, and UI elements"
    },
    {
        "name": "Realistic",
        "description": "Lifelike, high-detail photorealistic style.",
        "style_prompt": "ultra-realistic and highly detailed rendering, with accurate proportions, natural and diffused lighting, intricate surface textures, realistic skin tones and materials, subtle shadows and reflections, shallow depth of field, photographic clarity, and a lifelike atmosphere resembling high-resolution DSLR photography or cinematic stills"
    },
    {
        "name": "GameArt",
        "description": "Stylized, polished art ideal for modern games.",
        "style_prompt": "high-quality stylized digital game art with vibrant and saturated colors, semi-realistic proportions, smooth gradients and shading, crisp silhouettes, subtle 3D depth, exaggerated features for readability, polished surfaces with light reflections, and a professional finish inspired by AAA and mobile game concept art"
    }

]

def get_predefined_styles() -> StyleList:
    """Trả về danh sách styles có sẵn"""
    return PREDEFINED_STYLES

def get_predefined_styles_with_prompt(user_prompt: str) -> StyleList:
    """
    Trả về danh sách style có sẵn kết hợp với user prompt

    Args:
        user_prompt (str): Prompt gốc từ user (có thể để trống)

    Returns:
        StyleList: Danh sách styles đã kết hợp với user prompt (nếu có)
    """
    result = []

    # Nếu có user prompt thì kết hợp, không thì chỉ hiển thị style gốc
    has_user_prompt = user_prompt and user_prompt.strip()

    if has_user_prompt:
        user_prompt = user_prompt.strip()

    for style in PREDEFINED_STYLES:
        if has_user_prompt:
            # Kết hợp prompt: user prompt là chính, style chỉ là phong cách áp dụng
            combined_prompt = f"{user_prompt}, {style['style_prompt']}"
        else:
            # Chỉ hiển thị style prompt gốc
            combined_prompt = style['style_prompt']

        combined_style = {
            "name": style["name"],
            "description": style["description"],
            "style_prompt": combined_prompt
        }
        result.append(combined_style)

    return result

def get_style_by_name(style_name: str) -> dict:
    """
    Lấy một style cụ thể theo tên

    Args:
        style_name (str): Tên style cần tìm

    Returns:
        dict: Style object hoặc None nếu không tìm thấy
    """
    for style in PREDEFINED_STYLES:
        if style["name"].lower() == style_name.lower():
            return style
    return None

def get_style_names() -> list:
    """Trả về danh sách tên các styles có sẵn"""
    return [style["name"] for style in PREDEFINED_STYLES]

def get_random_styles(count: int = 5) -> StyleList:
    """
    Lấy ngẫu nhiên một số styles

    Args:
        count (int): Số lượng styles cần lấy (mặc định 5)

    Returns:
        StyleList: Danh sách styles ngẫu nhiên
    """
    import random

    if count > len(PREDEFINED_STYLES):
        count = len(PREDEFINED_STYLES)

    return random.sample(PREDEFINED_STYLES, count)
