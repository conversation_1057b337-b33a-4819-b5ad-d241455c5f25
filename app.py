import gradio as gr
from ai.llm import get_style_list_from_prompt, get_predefined_styles_list
from ai.openai_generator import generate_and_save_image_openai, AVAILABLE_MODELS
from utils.formatter import format_styles_for_display
import json

def generate_styles(user_prompt, mode, num_styles):
    try:
        # Xử lý user prompt
        user_prompt = user_prompt.strip() if user_prompt else ""

        if mode == "🤖 AI tạo style (Gemini)":
            # AI mode có thể hoạt động với hoặc không có user prompt
            styles = get_style_list_from_prompt(user_prompt, num_styles)
        else:  # "📋 Style có sẵn"
            # Predefined mode có thể hoạt động với hoặc không có user prompt
            styles = get_predefined_styles_list(user_prompt, 20)

        # Tạo checkbox choices cho styles
        style_choices = []
        for style in styles:
            choice_text = f"{style['name']}: {style['description']}"
            style_choices.append(choice_text)

        # Tr<PERSON> về formatted text, choices cho checkbox, và raw styles data
        formatted_text = format_styles_for_display(styles)
        styles_json = json.dumps(styles)  # Lưu raw data để dùng sau

        return formatted_text, gr.update(choices=style_choices, visible=True, value=[]), styles_json

    except Exception as e:
        error_msg = f"❌ Lỗi: {str(e)}"
        return error_msg, gr.update(choices=[], visible=False, value=[]), "[]"

def generate_image_from_prompt(prompt):
    """Tạo ảnh từ prompt sử dụng OpenAI DALL-E"""
    try:
        if not prompt or not prompt.strip():
            return None, "⚠️ Vui lòng nhập prompt để tạo ảnh."

        prompt = prompt.strip()
        print(f"🎨 Generating image for: {prompt}")

        # Tạo ảnh và lưu file sử dụng OpenAI
        filepath = generate_and_save_image_openai(prompt)

        # Trả về đường dẫn file để Gradio hiển thị
        return filepath, f"✅ Ảnh đã được tạo thành công!\nPrompt: {prompt}"

    except Exception as e:
        return None, f"❌ Lỗi khi tạo ảnh: {str(e)}"

def generate_images_from_selected_styles(selected_styles, styles_json_data, model_name, size, quality, user_prompt, mix_styles=False):
    """Tạo ảnh từ các styles đã chọn hoặc user prompt sử dụng OpenAI DALL-E"""
    try:
        # Kiểm tra có styles được chọn hoặc user prompt
        has_selected_styles = selected_styles and len(selected_styles) > 0
        has_user_prompt = user_prompt and user_prompt.strip()

        if not has_selected_styles and not has_user_prompt:
            return [], "⚠️ Vui lòng chọn ít nhất một style hoặc nhập user prompt để tạo ảnh."

        # Parse styles data (có thể empty nếu chỉ dùng user prompt)
        styles = json.loads(styles_json_data) if styles_json_data and styles_json_data != "[]" else []

        # Tạo danh sách styles để process
        selected_style_objects = []

        # Thêm selected styles từ checkbox
        if has_selected_styles:
            for selected in selected_styles:
                # Extract style name từ checkbox text (format: "Name: Description")
                style_name = selected.split(":")[0].strip()

                # Tìm style object tương ứng
                for style in styles:
                    if style['name'] == style_name:
                        selected_style_objects.append(style)
                        break

        # Nếu không có styles nào được chọn nhưng có user prompt, tạo ảnh từ user prompt
        if not selected_style_objects and has_user_prompt:
            user_prompt_obj = {
                "name": "User Prompt",
                "description": "Direct user prompt without style",
                "style_prompt": user_prompt.strip()
            }
            selected_style_objects.append(user_prompt_obj)

        if not selected_style_objects:
            return [], "❌ Không tìm thấy styles đã chọn hoặc user prompt hợp lệ."

        # Tạo ảnh cho từng style hoặc mix styles
        generated_images = []
        status_messages = []

        # Nếu mix_styles=True và có ít nhất 2 styles được chọn
        if mix_styles and len(selected_style_objects) >= 2:
            try:
                print(f"🎨 Generating mixed-style image from {len(selected_style_objects)} styles")
                
                # Trích xuất các style descriptors từ style prompts
                style_descriptors = []
                
                for style in selected_style_objects:
                    # Lấy phần mô tả style từ style_prompt
                    style_prompt = style['style_prompt']
                    
                    # Nếu có user prompt, loại bỏ nó khỏi style_prompt để lấy phần mô tả style thuần túy
                    if has_user_prompt:
                        user_prompt_clean = user_prompt.strip()
                        if style_prompt.startswith(user_prompt_clean):
                            # Loại bỏ user prompt từ đầu style_prompt
                            style_desc = style_prompt[len(user_prompt_clean):].strip()
                            # Loại bỏ dấu phẩy ở đầu nếu có
                            if style_desc.startswith(','):
                                style_desc = style_desc[1:].strip()
                        else:
                            # Nếu không tìm thấy user prompt ở đầu, lấy toàn bộ style_prompt
                            style_desc = style_prompt
                    else:
                        style_desc = style_prompt
                    
                    style_descriptors.append(style_desc)
                
                # Tạo mixed style descriptor
                style_names = [style['name'] for style in selected_style_objects]
                style_names_str = " and ".join(style_names)
                
                # Tạo mixed prompt
                if has_user_prompt:
                    # Tạo prompt mới không lặp lại user prompt
                    # Sử dụng "a single" để nhấn mạnh chỉ một đối tượng
                    mixed_prompt = f"A single {user_prompt.strip()} with a fusion of artistic styles: {style_names_str}. The image should blend these style elements: {', '.join(style_descriptors)}"
                else:
                    # Nếu không có user prompt, sử dụng style descriptors trực tiếp
                    mixed_prompt = f"An image with a fusion of artistic styles: {style_names_str}, with these characteristics: {', '.join(style_descriptors)}"
                
                # Tạo filename cho mixed style
                import re
                import time
                mixed_name = "Mixed_" + "_".join([style['name'] for style in selected_style_objects])
                clean_name = re.sub(r'[^\w\s-]', '', mixed_name)
                clean_name = re.sub(r'[-\s]+', '_', clean_name)
                timestamp = int(time.time())
                filename = f"{clean_name[:30]}_{timestamp}"
                
                # Lấy model từ AVAILABLE_MODELS
                model = AVAILABLE_MODELS.get(model_name, "dall-e-3")
                
                # Tạo ảnh sử dụng OpenAI DALL-E với mixed style
                filepath = generate_and_save_image_openai(mixed_prompt, filename, model, size, quality)
                generated_images.append(filepath)
                
                status_messages.append(f"✅ Mixed Style ({style_names_str}): Thành công")
                
            except Exception as e:
                status_messages.append(f"❌ Mixed Style: {str(e)}")
                print(f"Error generating mixed style: {e}")
        else:
            # Tạo ảnh cho từng style riêng biệt (cách cũ)
            for i, style in enumerate(selected_style_objects, 1):
                try:
                    print(f"🎨 Generating image {i}/{len(selected_style_objects)}: {style['name']}")

                    # Tạo filename từ style name
                    import re
                    import time
                    clean_name = re.sub(r'[^\w\s-]', '', style['name'])
                    clean_name = re.sub(r'[-\s]+', '_', clean_name)
                    timestamp = int(time.time())
                    filename = f"{clean_name}_{timestamp}"

                    # Lấy model từ AVAILABLE_MODELS
                    model = AVAILABLE_MODELS.get(model_name, "dall-e-3")

                    # Tạo ảnh sử dụng OpenAI DALL-E với custom size và quality
                    filepath = generate_and_save_image_openai(style['style_prompt'], filename, model, size, quality)
                    generated_images.append(filepath)
                    status_messages.append(f"✅ {style['name']}: Thành công")

                except Exception as e:
                    status_messages.append(f"❌ {style['name']}: {str(e)}")
                    print(f"Error generating {style['name']}: {e}")

        status_text = f"🎨 Đã tạo {len(generated_images)} ảnh:\n\n"
        status_text += "\n".join(status_messages)

        return generated_images, status_text

    except Exception as e:
        return [], f"❌ Lỗi: {str(e)}"

def update_size_choices(model_name):
    """Cập nhật size choices dựa trên model được chọn"""
    if model_name == "dall-e-3":
        return gr.update(
            choices=["1024x1024", "1792x1024", "1024x1792"],
            value="1024x1024",
            info="DALL-E-3 hỗ trợ: 1024x1024 (vuông), 1792x1024 (ngang), 1024x1792 (dọc)"
        )
    else:  # dall-e-2
        return gr.update(
            choices=["1024x1024", "512x512", "256x256"],
            value="1024x1024",
            info="DALL-E-2 hỗ trợ: 1024x1024, 512x512, 256x256 (chỉ hình vuông)"
        )

def update_quality_visibility(model_name):
    """Cập nhật visibility của quality selector dựa trên model"""
    if model_name == "dall-e-3":
        return gr.update(visible=True, info="Standard: $0.040 | HD: $0.080 (chỉ DALL-E-3)")
    else:  # dall-e-2
        return gr.update(visible=False, info="DALL-E-2 không hỗ trợ quality setting")

def combined_function(user_prompt, mode, num_styles, model_name, size, quality):
    """Hàm kết hợp tạo styles và tạo ảnh cho Interface"""
    try:
        # Bước 1: Tạo styles
        user_prompt = user_prompt.strip() if user_prompt else ""

        if mode == "🤖 AI tạo style (Gemini)":
            styles = get_style_list_from_prompt(user_prompt, num_styles)
        else:  # "� Style có sẵn"
            styles = get_predefined_styles_list(user_prompt, 20)

        # Format styles để hiển thị
        formatted_styles = format_styles_for_display(styles)

        # Bước 2: Tạo ảnh từ tất cả styles
        generated_images = []
        status_messages = []

        # Nếu có user prompt, tạo ảnh từ user prompt trước
        if user_prompt:
            try:
                print(f"🎨 Generating image from user prompt: {user_prompt}")
                import re
                import time
                clean_name = re.sub(r'[^\w\s-]', '', "user_prompt")
                timestamp = int(time.time())
                filename = f"{clean_name}_{timestamp}"

                model = AVAILABLE_MODELS.get(model_name, "dall-e-3")
                filepath = generate_and_save_image_openai(user_prompt, filename, model, size, quality)
                generated_images.append(filepath)
                status_messages.append(f"✅ User Prompt: Thành công")
            except Exception as e:
                status_messages.append(f"❌ User Prompt: {str(e)}")

        # Tạo ảnh từ các styles (tối đa 3 styles để tránh quá tải)
        for i, style in enumerate(styles[:3], 1):
            try:
                print(f"🎨 Generating image {i}/3: {style['name']}")

                import re
                import time
                clean_name = re.sub(r'[^\w\s-]', '', style['name'])
                clean_name = re.sub(r'[-\s]+', '_', clean_name)
                timestamp = int(time.time())
                filename = f"{clean_name}_{timestamp}"

                model = AVAILABLE_MODELS.get(model_name, "dall-e-3")
                filepath = generate_and_save_image_openai(style['style_prompt'], filename, model, size, quality)
                generated_images.append(filepath)
                status_messages.append(f"✅ {style['name']}: Thành công")

            except Exception as e:
                status_messages.append(f"❌ {style['name']}: {str(e)}")
                print(f"Error generating {style['name']}: {e}")

        status_text = f"🎨 Đã tạo {len(generated_images)} ảnh:\n\n"
        status_text += "\n".join(status_messages)

        return formatted_styles, generated_images, status_text

    except Exception as e:
        error_msg = f"❌ Lỗi: {str(e)}"
        return error_msg, [], error_msg

def main():
    with gr.Blocks(theme=gr.themes.Soft()) as demo:
        gr.Markdown("# 🎨 AI Style & Image Generator")
        gr.Markdown("Tạo style prompts và ảnh AI một cách đơn giản")
        
        # Hidden field to store styles data
        styles_json = gr.State(value="")
        
        with gr.Row():
            with gr.Column():
                # Input section
                user_prompt = gr.Textbox(
                    label="Nhập prompt mô tả nội dung ảnh",
                    placeholder="Ví dụ: một con mèo đang ngồi trên cửa sổ",
                    lines=2
                )
                
                mode = gr.Radio(
                    choices=["🤖 AI tạo style (Gemini)", "📋 Style có sẵn (5 styles)"],
                    value="📋 Style có sẵn (5 styles)",
                    label="Chọn phương thức tạo style"
                )
                
                num_styles = gr.Dropdown(
                    choices=[3, 5, 8],
                    value=5,
                    label="Số lượng styles"
                )
                
                # Button to generate styles only
                generate_styles_btn = gr.Button("🎨 Tạo Style Prompts")
                
                # Style selection
                style_choices = gr.CheckboxGroup(
                    label="Chọn styles để tạo ảnh",
                    choices=[],
                    visible=False
                )
                
                # Hidden field to store styles data
                styles_json = gr.State("")
                
                # Image generation settings
                model_name = gr.Dropdown(
                    choices=list(AVAILABLE_MODELS.keys()),
                    value="dall-e-3",
                    label="Chọn OpenAI Model"
                )
                
                size = gr.Dropdown(
                    choices=["1024x1024", "1792x1024", "1024x1792"],
                    value="1024x1024",
                    label="Kích thước ảnh"
                )
                
                quality = gr.Dropdown(
                    choices=["standard", "hd"],
                    value="standard",
                    label="Chất lượng (chỉ DALL-E-3)"
                )
                
                # Mix styles option
                mix_styles = gr.Checkbox(
                    label="Kết hợp styles đã chọn",
                    value=False,
                    info="Khi bật, sẽ tạo một ảnh kết hợp tất cả styles đã chọn thay vì tạo riêng từng ảnh"
                )
                
                # Button to generate images from selected styles
                generate_images_btn = gr.Button("🖼️ Tạo Ảnh từ Styles Đã Chọn")
            
            with gr.Column():
                # Output section
                styles_output = gr.Textbox(
                    label="Style prompts được tạo",
                    lines=10
                )
                
                images_output = gr.Gallery(
                    label="Ảnh được tạo",
                    columns=2,
                    rows=2
                )
                
                status_output = gr.Textbox(
                    label="Trạng thái",
                    lines=5
                )
        
        # Event handlers
        generate_styles_btn.click(
            fn=generate_styles,
            inputs=[user_prompt, mode, num_styles],
            outputs=[styles_output, style_choices, styles_json]
        )
        
        generate_images_btn.click(
            fn=generate_images_from_selected_styles,
            inputs=[style_choices, styles_json, model_name, size, quality, user_prompt, mix_styles],
            outputs=[images_output, status_output]
        )
        
        # Update size and quality options based on model
        model_name.change(
            fn=update_size_choices,
            inputs=model_name,
            outputs=size
        )
        
        model_name.change(
            fn=update_quality_visibility,
            inputs=model_name,
            outputs=quality
        )
    
    demo.launch()  # share=True

if __name__ == "__main__":
    main()
