import os
import requests
import json
from PIL import Image
from io import BytesIO
import base64
import time
from config import OPENAI_API_KEY

def generate_image_with_openai(prompt: str, model: str = "dall-e-3", size: str = "1024x1024", quality: str = "standard", style: str = "vivid") -> Image.Image:
    """
    Tạo ảnh sử dụng OpenAI DALL-E API

    Args:
        prompt (str): Text prompt để tạo ảnh
        model (str): Model name ("dall-e-3" hoặc "dall-e-2")
        size (str): <PERSON><PERSON><PERSON> thước ảnh ("1024x1024", "1792x1024", "1024x1792" cho DALL-E-3)
        quality (str): Chấ<PERSON> lượng ("standard" hoặc "hd" - chỉ cho DALL-E-3)
        style (str): Style ("vivid" hoặc "natural" - chỉ cho DALL-E-3)

    Returns:
        PIL.Image: Ảnh được tạo
    """
    if not OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY không được cấu hình. Vui lòng thêm OPENAI_API_KEY vào file .env")

    # Validate model-specific parameters
    if model == "dall-e-2":
        # DALL-E-2 chỉ hỗ trợ size 1024x1024, 512x512, 256x256
        if size not in ["1024x1024", "512x512", "256x256"]:
            size = "1024x1024"
        # DALL-E-2 không hỗ trợ quality và style
        quality = None
        style = None
    elif model == "dall-e-3":
        # DALL-E-3 hỗ trợ 1024x1024, 1792x1024, 1024x1792
        if size not in ["1024x1024", "1792x1024", "1024x1792"]:
            size = "1024x1024"
        # Validate quality và style
        if quality not in ["standard", "hd"]:
            quality = "standard"
        if style not in ["vivid", "natural"]:
            style = "vivid"

    print(f"🎨 Generating image with OpenAI...")
    print(f"📝 Model: {model}")
    print(f"📝 Prompt: {prompt}")
    print(f"📏 Size: {size}")
    if quality:
        print(f"🎯 Quality: {quality}")
    if style:
        print(f"🎨 Style: {style}")

    # Prepare API request
    url = "https://api.openai.com/v1/images/generations"
    headers = {
        "Authorization": f"Bearer {OPENAI_API_KEY}",
        "Content-Type": "application/json"
    }

    # Build request data
    data = {
        "model": model,
        "prompt": prompt,
        "n": 1,  # Số lượng ảnh tạo
        "size": size,
        "response_format": "url"  # Trả về URL thay vì base64
    }

    # Add DALL-E-3 specific parameters
    if model == "dall-e-3":
        if quality:
            data["quality"] = quality
        if style:
            data["style"] = style

    try:
        # Call OpenAI API
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()

        result = response.json()

        # Get image URL
        if "data" in result and len(result["data"]) > 0:
            image_url = result["data"][0]["url"]

            # Download image
            img_response = requests.get(image_url)
            img_response.raise_for_status()

            # Convert to PIL Image
            image = Image.open(BytesIO(img_response.content))
            print("✅ Image generated successfully!")
            return image
        else:
            raise ValueError("No image data returned from OpenAI API")

    except requests.exceptions.RequestException as e:
        error_msg = str(e)
        print(f"❌ Error: {error_msg}")

        # Handle specific OpenAI errors
        if response.status_code == 400:
            raise ValueError(f"⚠️ Bad Request: Prompt có thể vi phạm content policy hoặc quá dài. Hãy thử prompt khác.")
        elif response.status_code == 401:
            raise ValueError(f"🔑 Unauthorized: OPENAI_API_KEY không hợp lệ. Vui lòng kiểm tra API key.")
        elif response.status_code == 429:
            raise ValueError(f"🔄 Rate Limited: Đã vượt quá giới hạn API. Vui lòng thử lại sau.")
        elif response.status_code == 500:
            raise ValueError(f"🔧 Server Error: OpenAI server đang gặp vấn đề. Vui lòng thử lại sau.")
        else:
            raise ValueError(f"OpenAI API error: {error_msg}")

    except Exception as e:
        raise ValueError(f"Unexpected error: {str(e)}")


def save_image(image: Image.Image, filename: str) -> str:
    """
    Lưu ảnh vào file

    Args:
        image (PIL.Image): Ảnh cần lưu
        filename (str): Tên file (không cần extension)

    Returns:
        str: Đường dẫn file đã lưu
    """
    filepath = f"generated_images/{filename}.png"

    # Tạo thư mục nếu chưa có
    os.makedirs("generated_images", exist_ok=True)

    image.save(filepath)
    print(f"💾 Image saved to: {filepath}")
    return filepath

def generate_and_save_image_openai(prompt: str, filename: str = None, model: str = "dall-e-3", size: str = "1024x1024", quality: str = "standard", style: str = "vivid") -> str:
    """
    Tạo và lưu ảnh sử dụng OpenAI DALL-E

    Args:
        prompt (str): Text prompt
        filename (str): Tên file (optional, sẽ auto-generate nếu không có)
        model (str): OpenAI model ("dall-e-3" hoặc "dall-e-2")
        size (str): Kích thước ảnh
        quality (str): Chất lượng (chỉ cho DALL-E-3)
        style (str): Style (chỉ cho DALL-E-3)

    Returns:
        str: Đường dẫn file đã lưu
    """
    if not filename:
        # Tạo filename từ prompt
        import re
        clean_prompt = re.sub(r'[^\w\s-]', '', prompt)[:30]
        clean_prompt = re.sub(r'[-\s]+', '_', clean_prompt)
        timestamp = int(time.time())
        filename = f"{clean_prompt}_{timestamp}"

    image = generate_image_with_openai(prompt, model, size, quality, style)
    filepath = save_image(image, filename)
    return filepath



# Available OpenAI models
AVAILABLE_MODELS = {
    "dall-e-3": "dall-e-3",
    "dall-e-2": "dall-e-2"
}

# Model capabilities
MODEL_CAPABILITIES = {
    "dall-e-3": {
        "sizes": ["1024x1024", "1792x1024", "1024x1792"],
        "quality": ["standard", "hd"],
        "style": ["vivid", "natural"],
        "max_prompt_length": 4000,
        "cost_standard": "$0.040",
        "cost_hd": "$0.080",
        "note": "🚀 Latest DALL-E model with best quality and prompt understanding"
    },
    "dall-e-2": {
        "sizes": ["1024x1024", "512x512", "256x256"],
        "quality": None,
        "style": None,
        "max_prompt_length": 1000,
        "cost": "$0.020",
        "note": "🎨 Previous generation, faster and cheaper"
    }
}
